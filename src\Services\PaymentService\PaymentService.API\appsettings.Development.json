{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"}}, {"Name": "File", "Args": {"path": "logs/dev-payment-service-.log"}}]}}