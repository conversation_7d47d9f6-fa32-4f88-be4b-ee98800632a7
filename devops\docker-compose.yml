# Common service settings for application services
x-service-defaults: &service-defaults
  restart: unless-stopped
  networks:
    - app_network
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"

# Common settings for microservices
x-microservices-defaults: &microservices-defaults
  <<: *service-defaults
  environment: &microservices-environment
    HTTP_PORTS: ""
    HTTPS_PORTS: ""
    ConnectionStrings__PostgresConnection: "Host=${POSTGRES_HOST};Port=${POSTGRES_PORT};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}"
    ASPNETCORE_ENVIRONMENT: "${ENVIRONMENT}"
    RabbitMQ__HostName: "${RABBITMQ_HOST}"
    RabbitMQ__UserName: "${RABBITMQ_USER}"
    RabbitMQ__Password: "${RABBITMQ_PASSWORD}"
    RabbitMQ__VirtualHost: "${RABBITMQ_VHOST}"
    RabbitMQ__Port: "${RABBITMQ_PORT}"
    RabbitMQ__RetryCount: "${RABBITMQ_RETRY_COUNT}"
    JWT__SecretKey: "${JWT_SECRET_KEY}"
    JWT__Issuer: "${JWT_ISSUER}"
    JWT__Audience: "${JWT_AUDIENCE}"
    JWT__AccessTokenExpirationInMinutes: "${JWT_ACCESS_TOKEN_EXPIRATION_MINUTES}"
    JWT__RefreshTokenExpirationInDays: "${JWT_REFRESH_TOKEN_EXPIRATION_DAYS}"
    JWT__RefreshTokenSlidingExpirationInMinutes: "${JWT_REFRESH_TOKEN_SLIDING_EXPIRATION_MINUTES}"
    JWT__TokenType: "${JWT_TOKEN_TYPE}"
  depends_on:
    postgres:
      condition: service_healthy
    redis:
      condition: service_healthy
    rabbitmq:
      condition: service_healthy

x-minio-environment: &minio-environment
  Minio__Endpoint: http://${MINIO_HOST}:${MINIO_PORT}
  Minio__AccessKey: ${MINIO_USER}
  Minio__SecretKey: ${MINIO_PASSWORD}
  Minio__UseSSL: ${MINIO_USE_SSL}
  Minio__BucketName: ${MINIO_IDENTITY_BUCKET}

x-functional-service-environment: &functional-service-environment
  <<: *microservices-environment
  ServiceName: Functional
  ASPNETCORE_URLS: "http://*:${FUNCTIONAL_HTTP_PORT}"
  SmtpSetting__Server: ${SMTP_SERVER}
  SmtpSetting__Port: ${SMTP_PORT}
  SmtpSetting__Username: ${SMTP_USERNAME}
  SmtpSetting__Password: ${SMTP_PASSWORD}
  SmtpSetting__SenderEmail: ${SMTP_SENDER_EMAIL}
  SmtpSetting__SenderName: ${SMTP_SENDER_NAME}

x-identity-service-environment: &identity-service-environment
  <<: [*microservices-environment, *minio-environment]
  ServiceName: Identity
  Kestrel__Endpoints__Http__Url: http://*:${IDENTITY_HTTP_PORT}
  Kestrel__Endpoints__Http__Protocols: Http1
  Kestrel__Endpoints__Grpc__Url: https://*:${IDENTITY_HTTP2_PORT}
  Kestrel__Endpoints__Grpc__Protocols: Http2
  Kestrel__Endpoints__Grpc__Certificate__Path: ${IDENTITY_CERTIFICATE_PATH}
  Kestrel__Endpoints__Grpc__Certificate__Password: ${IDENTITY_CERTIFICATE_PASSWORD}
  ConnectionStrings__RedisConnection: ${REDIS_HOST}:${REDIS_PORT},password=${REDIS_PASSWORD},defaultDatabase=0
  Google__ClientId: ${GOOGLE_CLIENT_ID}
  Google__ClientSecret: ${GOOGLE_CLIENT_SECRET}
  ConfirmationCode__AllowedChars: ${CONFIRMATION_CODE_ALLOWED_CHARS}
  ConfirmationCode__CodeLength: ${CONFIRMATION_CODE_LENGTH}
  ConfirmationCode__ExpirationTimeInMinutes: ${CONFIRMATION_CODE_EXPIRATION_MINUTES}
  ConfirmationCode__MaximumAttempts: ${CONFIRMATION_CODE_MAX_ATTEMPTS}
  # Cấu hình DataProtection
  DataProtection__CertificateThumbprint: ${DATA_PROTECTION_CERT_THUMBPRINT:-}
  DataProtection__UseX509Certificate: ${DATA_PROTECTION_USE_CERT:-false}

x-property-service-environment: &property-service-environment
  <<: [*microservices-environment, *minio-environment]
  ServiceName: Property
  Kestrel__Endpoints__Http__Url: http://*:${PROPERTY_HTTP_PORT}
  Kestrel__Endpoints__Http__Protocols: Http1
  Kestrel__Endpoints__Grpc__Url: https://*:${PROPERTY_HTTP2_PORT}
  Kestrel__Endpoints__Grpc__Protocols: Http2
  Kestrel__Endpoints__Grpc__Certificate__Path: ${PROPERTY_CERTIFICATE_PATH}
  Kestrel__Endpoints__Grpc__Certificate__Password: ${PROPERTY_CERTIFICATE_PASSWORD}
  ConnectionStrings__RedisConnection: ${REDIS_HOST}:${REDIS_PORT},password=${REDIS_PASSWORD},defaultDatabase=1
  Minio__Endpoint: http://${MINIO_HOST}:${MINIO_PORT}
  Minio__AccessKey: ${MINIO_USER}
  Minio__SecretKey: ${MINIO_PASSWORD}
  Minio__UseSSL: ${MINIO_USE_SSL}
  Minio__BucketName: ${MINIO_PROPERTY_BUCKET}

x-payment-service-environment: &payment-service-environment
  <<: *microservices-environment
  ServiceName: Payment
  Kestrel__Endpoints__Http__Url: http://*:${PAYMENT_HTTP_PORT}
  Kestrel__Endpoints__Http__Protocols: Http1
  Kestrel__Endpoints__Grpc__Url: https://*:${PAYMENT_HTTP2_PORT}
  Kestrel__Endpoints__Grpc__Protocols: Http2
  Kestrel__Endpoints__Grpc__Certificate__Path: ${PAYMENT_CERTIFICATE_PATH}
  Kestrel__Endpoints__Grpc__Certificate__Password: ${PAYMENT_CERTIFICATE_PASSWORD}
  ConnectionStrings__RedisConnection: ${REDIS_HOST}:${REDIS_PORT},password=${REDIS_PASSWORD},defaultDatabase=2
  Paypal__ClientId: ${PAYPAL_CLIENT_ID}
  Paypal__ClientSecret: ${PAYPAL_CLIENT_SECRET}
  Paypal__UseSandbox: ${PAYPAL_USE_SANDBOX}

services:
  # Infrastructure Services
  postgres:
    <<: *service-defaults
    image: postgres:16-alpine
    container_name: ${PROJECT_PREFIX}-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    <<: *service-defaults
    image: redis:alpine
    container_name: ${PROJECT_PREFIX}-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    <<: *service-defaults
    image: minio/minio:latest
    container_name: ${PROJECT_PREFIX}-minio
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD}
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 5

  rabbitmq:
    <<: *service-defaults
    image: rabbitmq:management-alpine
    container_name: ${PROJECT_PREFIX}-rabbitmq
    ports:
      - "${RABBITMQ_PORT}:5672"
      - "${RABBITMQ_MANAGEMENT_PORT}:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Application Services
  functional-service-1:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${FUNCTIONAL_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-functional-service-1
    environment:
      <<: *functional-service-environment
    volumes:
      - ./services/functional/logs:/app/logs
      - ./services/functional/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${FUNCTIONAL_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  functional-service-2:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${FUNCTIONAL_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-functional-service-2
    environment:
      <<: *functional-service-environment
    volumes:
      - ./services/functional/logs:/app/logs
      - ./services/functional/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${FUNCTIONAL_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  identity-service-1:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${IDENTITY_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-identity-service-1
    environment:
      <<: *identity-service-environment
    volumes:
      - ./services/identity/logs:/app/logs
      - ./services/identity/ssl:/app/ssl
      - ./services/identity/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${IDENTITY_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  identity-service-2:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${IDENTITY_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-identity-service-2
    environment:
      <<: *identity-service-environment
    volumes:
      - ./services/identity/logs:/app/logs
      - ./services/identity/ssl:/app/ssl
      - ./services/identity/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${IDENTITY_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  property-service-1:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${PROPERTY_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-property-service-1
    environment:
      <<: *property-service-environment
    volumes:
      - ./services/property/logs:/app/logs
      - ./services/property/ssl:/app/ssl
      - ./services/property/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${PROPERTY_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  property-service-2:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${PROPERTY_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-property-service-2
    environment:
      <<: *property-service-environment
    volumes:
      - ./services/property/logs:/app/logs
      - ./services/property/ssl:/app/ssl
      - ./services/property/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${PROPERTY_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  payment-service-1:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${PAYMENT_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-payment-service-1
    environment:
      <<: *payment-service-environment
    volumes:
      - ./services/payment/logs:/app/logs
      - ./services/payment/ssl:/app/ssl
      - ./services/payment/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${PAYMENT_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  payment-service-2:
    <<: *microservices-defaults
    build:
      context: ${SERVICE_CONTEXT}
      dockerfile: ${PAYMENT_SERVICE_DOCKERFILE}
      args:
        ENVIRONMENT: ${ENVIRONMENT}
    container_name: ${PROJECT_PREFIX}-payment-service-2
    environment:
      <<: *payment-service-environment
    volumes:
      - ./services/payment/logs:/app/logs
      - ./services/payment/ssl:/app/ssl
      - ./services/payment/dataprotection-keys:/root/.aspnet/DataProtection-Keys
    healthcheck:
      test:
        ["CMD", "curl", "-f", "http://localhost:${PAYMENT_HTTP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  nginx:
    <<: *service-defaults
    build:
      context: ${NGINX_CONTEXT}
      dockerfile: ${NGINX_DOCKERFILE}
    container_name: ${PROJECT_PREFIX}-nginx
    ports:
      - "${HTTP_PORT}:80"
      - "${HTTPS_PORT}:443"
    environment:
      PROJECT_PREFIX: ${PROJECT_PREFIX}
      FUNCTIONAL_HTTP_PORT: ${FUNCTIONAL_HTTP_PORT}
      IDENTITY_HTTP_PORT: ${IDENTITY_HTTP_PORT}
      IDENTITY_HTTP2_PORT: ${IDENTITY_HTTP2_PORT}
      PROPERTY_HTTP_PORT: ${PROPERTY_HTTP_PORT}
      PROPERTY_HTTP2_PORT: ${PROPERTY_HTTP2_PORT}
      PAYMENT_HTTP_PORT: ${PAYMENT_HTTP_PORT}
      PAYMENT_HTTP2_PORT: ${PAYMENT_HTTP2_PORT}
    volumes:
      - ./nginx/nginx.conf.template:/etc/nginx/nginx.conf.template
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
      - ./nginx/cache:/var/cache/nginx
      - ./nginx/docker-entrypoint.sh:/docker-entrypoint.sh
    depends_on:
      - functional-service-1
      - functional-service-2
      - identity-service-1
      - identity-service-2
      - property-service-1
      - property-service-2
      - payment-service-1
      - payment-service-2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app_network:
    driver: bridge
    name: ${PROJECT_PREFIX}_network

volumes:
  postgres_data:
    name: ${PROJECT_PREFIX}_postgres_data
  redis_data:
    name: ${PROJECT_PREFIX}_redis_data
  minio_data:
    name: ${PROJECT_PREFIX}_minio_data
  rabbitmq_data:
    name: ${PROJECT_PREFIX}_rabbitmq_data
