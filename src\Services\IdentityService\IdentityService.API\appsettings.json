{"ServiceName": "Identity", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://*:5001", "Protocols": "Http1"}, "Grpc": {"Url": "http://*:50051", "Protocols": "Http2"}}}, "ConnectionStrings": {"PostgresConnection": "Host=localhost;Database=estate_elite;Username=lau;Password=**********", "RedisConnection": "localhost:6379,password=**********,defaultDatabase=0"}, "RabbitMQ": {"HostName": "localhost", "UserName": "lau", "Password": "**********", "VirtualHost": "/", "Port": 5672, "RetryCount": 10}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/identity-service-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "ConfirmationCode": {"AllowedChars": "0123456789", "CodeLength": 6, "ExpirationTimeInMinutes": 5, "MaximumAttempts": 3}, "JWT": {"SecretKey": "ZTNtMWJkOTI0eGo5NjN4azQ1aTByYWp4dmVvb3pnbzV2ZTR6cmZwMzR2eGJyNXlibnN4eXJicDdudHh2cnl3bHR1NmVmczVmaG1kNDR2cDYxZ3Vya2JkbGIxMGhwYnhmcnAzOWF4aGg3MHVrMnc0M3dzOWk0NW8zNThqazh4aWg=", "Issuer": "https://localhost:5001", "Audience": "https://localhost:3000", "AccessTokenExpirationInMinutes": 30, "RefreshTokenExpirationInDays": 1, "RefreshTokenSlidingExpirationInMinutes": 30, "TokenType": "Bearer"}, "Minio": {"Endpoint": "http://localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "UseSSL": false, "BucketName": "identity-service"}, "Google": {"ClientId": "281396792770-jl6lcie6lh86hb0t7g77hopafris4jc4.apps.googleusercontent.com", "ClientSecret": "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"}, "DataProtection": {"UseX509Certificate": false, "CertificateThumbprint": ""}, "GrpcEndpoint": {"Identity": "http://localhost:50051", "Property": "http://localhost:50052", "Payment": "http://localhost:50053"}}