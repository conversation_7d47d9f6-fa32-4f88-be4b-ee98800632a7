{"ServiceName": "Payment", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://*:5003", "Protocols": "Http1"}, "Grpc": {"Url": "http://*:50053", "Protocols": "Http2"}}}, "ConnectionStrings": {"PostgresConnection": "Host=localhost;Database=estate_elite;Username=lau;Password=**********", "RedisConnection": "localhost:6379,password=**********,defaultDatabase=2"}, "RabbitMQ": {"HostName": "localhost", "UserName": "lau", "Password": "**********", "VirtualHost": "/", "Port": 5672, "RetryCount": 10}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/payment-service-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "JWT": {"SecretKey": "ZTNtMWJkOTI0eGo5NjN4azQ1aTByYWp4dmVvb3pnbzV2ZTR6cmZwMzR2eGJyNXlibnN4eXJicDdudHh2cnl3bHR1NmVmczVmaG1kNDR2cDYxZ3Vya2JkbGIxMGhwYnhmcnAzOWF4aGg3MHVrMnc0M3dzOWk0NW8zNThqazh4aWg=", "Issuer": "https://localhost:5001", "Audience": "https://localhost:3000", "AccessTokenExpirationInMinutes": 30, "RefreshTokenExpirationInDays": 1, "RefreshTokenSlidingExpirationInMinutes": 30, "TokenType": "Bearer"}, "Paypal": {"ClientId": "AeBK-cHjAhtvRwvripoItrx99T3ExFXE93hFCoeX5RwO9pJAKIGxk9qgq2dx_wG2LA9b_kRj7EZW5Ck6", "ClientSecret": "EFQT-12pjRQUF5sqWZAo1UW-9soxa9f7-sqLYt6RO940FNpGGiOZsJxNz4sfqSdVRHjhxkEoITE_t6y3", "UseSandbox": true}, "DataProtection": {"UseX509Certificate": false, "CertificateThumbprint": ""}, "GrpcEndpoint": {"Identity": "http://localhost:50051", "Property": "http://localhost:50052", "Payment": "http://localhost:50053"}}