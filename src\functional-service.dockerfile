# ===== STAGE 1: BUILD =====
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files first for better layer caching
COPY Services/FunctionalService/FunctionalService.csproj Services/FunctionalService/

COPY BuildingBlocks/EventBus/EventBus.Infrastructures/EventBus.Infrastructures.csproj BuildingBlocks/EventBus/EventBus.Infrastructures/
COPY BuildingBlocks/EventBus/EventBus.RabbitMQ/EventBus.RabbitMQ.csproj BuildingBlocks/EventBus/EventBus.RabbitMQ/
COPY BuildingBlocks/SharedKernel/SharedKernel.csproj BuildingBlocks/SharedKernel/
COPY BuildingBlocks/DistributedCache/DistributedCache.Redis/DistributedCache.Redis.csproj BuildingBlocks/DistributedCache/DistributedCache.Redis/

# Restore packages
RUN dotnet restore Services/FunctionalService/FunctionalService.csproj

# Copy everything else and maintain directory structure
COPY Services/FunctionalService/ /src/Services/FunctionalService/
COPY BuildingBlocks/ /src/BuildingBlocks/

# Build the project
WORKDIR /src/Services/FunctionalService
RUN dotnet publish -c Release -o /app/publish

# ===== STAGE 2: RUNTIME =====
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app

# Install curl for health checks
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# Copy build output from build stage
COPY --from=build /app/publish .

# Create directory for logs
RUN mkdir -p /app/logs

# Expose both HTTP and HTTPS ports
EXPOSE 5000

ENTRYPOINT ["dotnet", "FunctionalService.dll"]