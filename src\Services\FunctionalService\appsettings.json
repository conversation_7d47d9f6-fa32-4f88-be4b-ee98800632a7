{"ServiceName": "Functional", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "RabbitMQ": {"HostName": "localhost", "UserName": "lau", "Password": "lau2962003", "VirtualHost": "/", "Port": 5672, "RetryCount": 10}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/functional-service-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "SmtpSetting": {"Server": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "ccca pxxp hijl iuwd", "SenderEmail": "<EMAIL>", "SenderName": "Estate Elite"}, "DataProtection": {"UseX509Certificate": false, "CertificateThumbprint": ""}}