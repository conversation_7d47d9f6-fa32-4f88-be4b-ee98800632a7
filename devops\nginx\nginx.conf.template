user nginx;
worker_processes auto; # Automatically determine the number of CPU cores

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 2048;       # Increased from 1024
    multi_accept on;               # Accept as many connections as possible
    use epoll;                     # Efficient connection processing for Linux
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Enhanced logging format with request timing and compression info
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      '$request_time $upstream_response_time $gzip_ratio';

    # Specialized gRPC logging format
    log_format grpc_log  '$remote_addr - $remote_user [$time_local] "$request" '
                         '$status $bytes_sent "$http_referer" '
                         '"$http_user_agent" $upstream_response_time';

    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log warn;

    # Performance optimizations
    sendfile        on;
    tcp_nopush      on;            # Optimize data sending
    tcp_nodelay     on;            # Disable <PERSON><PERSON>'s algorithm
    keepalive_timeout  65;
    keepalive_requests 1000;       # Increased from 100
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;              # Added send timeout
    reset_timedout_connection on;  # Reset timed out connections

    # File descriptor caching
    open_file_cache max=5000 inactive=30s;  # Increased cache size
    open_file_cache_valid 60s;              # Increased validity period
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # Enable compression - optimized settings
    gzip  on;
    gzip_comp_level 5;             # Balanced between CPU and compression ratio
    gzip_min_length 256;           # Smaller threshold for compression
    gzip_types
        text/plain
        text/css
        text/javascript
        application/javascript
        application/json
        application/xml
        application/x-font-ttf
        application/x-font-opentype
        application/vnd.ms-fontobject
        image/svg+xml
        image/x-icon;
    gzip_proxied any;              # Compress for all proxied requests
    gzip_vary on;
    gzip_disable "msie6";          # Disable for old IE versions

    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self' data:;" always;

    # Rate limiting zones - separate zones for HTTP and gRPC
    limit_req_zone $binary_remote_addr zone=http_limit:10m rate=20r/s;  # Increased rate for HTTP
    limit_req_zone $binary_remote_addr zone=grpc_limit:10m rate=100r/s;  # Much higher rate for gRPC

    # Buffer sizes - increase for gRPC
    proxy_buffer_size 32k;  # Increased from 16k
    proxy_buffers 16 32k;   # Increased from 8 16k
    proxy_busy_buffers_size 64k;  # Increased from 32k

    # --- Upstream REST API Definitions ---
    upstream identity_service {
        least_conn;
        server ${PROJECT_PREFIX}-identity-service-1:${IDENTITY_HTTP_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-identity-service-2:${IDENTITY_HTTP_PORT} backup;
        keepalive 64;  # Increased connections
    }

    upstream functional_service {
        least_conn;
        server ${PROJECT_PREFIX}-functional-service-1:${FUNCTIONAL_HTTP_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-functional-service-2:${FUNCTIONAL_HTTP_PORT} backup;
        keepalive 64;
    }

    upstream property_service {
        least_conn;
        server ${PROJECT_PREFIX}-property-service-1:${PROPERTY_HTTP_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-property-service-2:${PROPERTY_HTTP_PORT} backup;
        keepalive 64;
    }

    upstream payment_service {
        least_conn;
        server ${PROJECT_PREFIX}-payment-service-1:${PAYMENT_HTTP_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-payment-service-2:${PAYMENT_HTTP_PORT} backup;
        keepalive 64;
    }
    # --- End REST API Upstream Definitions ---

    # --- Upstream gRPC Definitions ---
    upstream identity_service_grpc {
        least_conn;
        server ${PROJECT_PREFIX}-identity-service-1:${IDENTITY_HTTP2_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-identity-service-2:${IDENTITY_HTTP2_PORT} backup;
        keepalive 32;
    }

    upstream property_service_grpc {
        least_conn;
        server ${PROJECT_PREFIX}-property-service-1:${PROPERTY_HTTP2_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-property-service-2:${PROPERTY_HTTP2_PORT} backup;
        keepalive 32;
    }

    upstream payment_service_grpc {
        least_conn;
        server ${PROJECT_PREFIX}-payment-service-1:${PAYMENT_HTTP2_PORT} max_fails=3 fail_timeout=30s;
        server ${PROJECT_PREFIX}-payment-service-2:${PAYMENT_HTTP2_PORT} backup;
        keepalive 32;
    }
    # --- End gRPC Upstream Definitions ---

    # Server block for HTTP (Port 80) - Redirect to HTTPS
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name localhost estate-elite.com www.estate-elite.com;
        server_tokens off;  # Don't show nginx version

        # Monitor URI for health checks
        location /health {
            access_log off;
            add_header Content-Type text/plain;
            return 200 "Nginx is healthy\n";
        }

        # Redirect all HTTP traffic to HTTPS
        location / {
            return 308 https://$host$request_uri;
        }
    }

    # Server block for HTTPS (Port 443)
    server {
        listen 443 ssl default_server;
        listen [::]:443 ssl default_server;
        http2 on;

        server_name localhost estate-elite.com www.estate-elite.com;
        server_tokens off;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/nginx.crt;
        ssl_certificate_key /etc/nginx/ssl/nginx.key;

        # Optimized SSL
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
        ssl_session_cache shared:SSL_HTTP:20m;         # Increased cache size
        ssl_session_timeout 4h;                   # Increased session timeout
        ssl_session_tickets off;
        ssl_dhparam /etc/nginx/ssl/nginx.pem;   # Generate with: openssl dhparam -out /etc/nginx/ssl/nginx.pem 2048

        # OCSP Stapling - disabled until proper certificate chain is available
        # ssl_stapling on;
        # ssl_stapling_verify on;
        resolver ******* ******* valid=300s;      # Google DNS for resolving OCSP responder
        resolver_timeout 5s;

        # HSTS (HTTP Strict Transport Security)
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

        # Health check endpoint
        location /health {
            access_log off;
            add_header Content-Type text/plain;
            return 200 "Nginx is healthy\n";
        }

        # --- Location Blocks for REST API Microservices ---
        location /identity/ {
            proxy_pass http://identity_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Connection optimization
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Rate limiting with burst capability
            limit_req zone=http_limit burst=20 nodelay;
            
            # Enable caching for GET requests
            proxy_cache api_cache;
            proxy_cache_methods GET;
            proxy_cache_valid 200 5m;
            proxy_cache_key $scheme$host$request_uri$request_method;
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Cache-Status $upstream_cache_status;
        }

        location /functional/ {
            proxy_pass http://functional_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Connection optimization
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Rate limiting with burst capability
            limit_req zone=http_limit burst=20 nodelay;
            
            # Enable caching for GET requests
            proxy_cache api_cache;
            proxy_cache_methods GET;
            proxy_cache_valid 200 5m;
            proxy_cache_key $scheme$host$request_uri$request_method;
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Cache-Status $upstream_cache_status;
        }

        location /property/ {
            proxy_pass http://property_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Connection optimization
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Rate limiting with burst capability
            limit_req zone=http_limit burst=20 nodelay;
            
            # Enable caching for GET requests
            proxy_cache api_cache;
            proxy_cache_methods GET;
            proxy_cache_valid 200 5m;
            proxy_cache_key $scheme$host$request_uri$request_method;
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Cache-Status $upstream_cache_status;
        }

        location /payment/ {
            proxy_pass http://payment_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Connection optimization
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Rate limiting with burst capability
            limit_req zone=http_limit burst=20 nodelay;
            
            # Enable caching for GET requests
            proxy_cache api_cache;
            proxy_cache_methods GET;
            proxy_cache_valid 200 5m;
            proxy_cache_key $scheme$host$request_uri$request_method;
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Cache-Status $upstream_cache_status;
        }
        # --- End REST API Location Blocks ---

        # --- Location Blocks for gRPC Microservices ---
        # Separate log file for gRPC requests
        access_log /var/log/nginx/grpc_access.log grpc_log;
        error_log /var/log/nginx/grpc_error.log warn;

        # Identity Service gRPC endpoint
        location /grpc/identity/ {
            # Remove trailing slash from location and forward exact path
            rewrite ^/grpc/identity/(.*) /$1 break;
            
            # Use grpcs:// since your backend is configured with TLS
            grpc_pass grpcs://identity_service_grpc;
            
            # SSL verification - optional if using self-signed certs
            grpc_ssl_verify off;
            grpc_ssl_server_name on;
            
            # Client buffer size for headers
            grpc_buffer_size 16k;
            
            # Connection parameters
            grpc_connect_timeout 5s;
            grpc_read_timeout 60s;
            grpc_send_timeout 60s;
            
            # Rate limiting with burst capability
            limit_req zone=grpc_limit burst=30 nodelay;
            
            # Add request headers
            grpc_set_header Host $host;
            grpc_set_header X-Real-IP $remote_addr;
            grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            grpc_set_header X-Forwarded-Proto $scheme;  
            
            # Error handling
            grpc_intercept_errors on;
            error_page 502 504 /grpc-unavailable;
        }
        
        # Property Service gRPC endpoint
        location /grpc/property/ {            # Remove trailing slash from location and forward exact path
            rewrite ^/grpc/property/(.*) /$1 break;
            grpc_pass grpcs://property_service_grpc;

            grpc_ssl_verify off;
            grpc_ssl_server_name on;
            
            grpc_buffer_size 16k;
            
            grpc_connect_timeout 5s;
            grpc_read_timeout 60s;
            grpc_send_timeout 60s;
            
            limit_req zone=grpc_limit burst=30 nodelay;
            
            grpc_set_header Host $host;
            grpc_set_header X-Real-IP $remote_addr;
            grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            grpc_set_header X-Forwarded-Proto $scheme;
            
            grpc_intercept_errors on;
            error_page 502 504 /grpc-unavailable;
        }
        
        # Payment Service gRPC endpoint
        location /grpc/payment/ {
            rewrite ^/grpc/payment/(.*) /$1 break;
            grpc_pass grpcs://payment_service_grpc;

            grpc_ssl_verify off;
            grpc_ssl_server_name on;
            
            grpc_buffer_size 16k;
            
            grpc_connect_timeout 5s;
            grpc_read_timeout 60s;
            grpc_send_timeout 60s;
            
            limit_req zone=grpc_limit burst=30 nodelay;
            
            grpc_set_header Host $host;
            grpc_set_header X-Real-IP $remote_addr;
            grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            grpc_set_header X-Forwarded-Proto $scheme;
            
            grpc_intercept_errors on;
            error_page 502 504 /grpc-unavailable;
        }
        
        # Error handling for gRPC services
        location = /grpc-unavailable {
            internal;
            default_type application/grpc;
            add_header grpc-status 14; # UNAVAILABLE in gRPC status code
            add_header grpc-message "Service temporarily unavailable";
            return 204;
        }
        # --- End gRPC Location Blocks ---

        # Default location for static files
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ =404;
        }

        # Custom logging for HTTPS
        access_log /var/log/nginx/https_access.log main;
        error_log /var/log/nginx/https_error.log warn;
    }

    # Define cache settings for API responses
    proxy_cache_path /var/cache/nginx/api_cache levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m;
    
    # Define cache settings for gRPC responses (if needed)
    # proxy_cache_path /var/cache/nginx/grpc_cache levels=1:2 keys_zone=grpc_cache:10m max_size=500m inactive=30m;
}