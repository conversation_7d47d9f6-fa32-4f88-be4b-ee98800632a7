import { LISTING_TYPE, PROPERTY_STATUS } from '@/lib/enum';
import { Property } from './type';

export const sampleProperties: Property[] = [
  {
    id: '1',
    title: '<PERSON><PERSON>n hộ Vinhomes Central Park',
    address: '208 <PERSON><PERSON><PERSON><PERSON>, Bình Thạnh, TP.HCM',
    price: 3500000000,
    status: PROPERTY_STATUS.ACTIVE,
    type: '<PERSON><PERSON><PERSON> hộ',
    area: 85,
    bedrooms: 2,
    bathrooms: 2,
    createdAt: '2025-03-15',
    listingType: LISTING_TYPE.SALE,
    image: 'https://example.com/image1.jpg',
  },
  {
    id: '2',
    title: '<PERSON><PERSON><PERSON> phố <PERSON>h<PERSON>o Điền',
    address: '12 Quốc Hương, Thảo Điền, Quận 2, TP.HCM',
    price: 12000000000,
    status: PROPERTY_STATUS.ACTIVE,
    type: 'Nhà phố',
    area: 200,
    bedrooms: 4,
    bathrooms: 3,
    createdAt: '2025-03-10',
    listingType: LISTING_TYPE.RENT,
    image: 'https://example.com/image1.jpg',
  },
  {
    id: '3',
    title: '<PERSON><PERSON><PERSON><PERSON> thự Palm City',
    address: 'Palm City, Quận 9, TP.HCM',
    price: 18000000000,
    status: PROPERTY_STATUS.ACTIVE,
    type: 'Biệt thự',
    area: 350,
    bedrooms: 5,
    bathrooms: 5,
    createdAt: '2025-02-28',
    listingType: LISTING_TYPE.RENT,
    image: 'https://example.com/image1.jpg',
  },
  {
    id: '4',
    title: 'Đất nền Nhơn Trạch',
    address: 'Nhơn Trạch, Đồng Nai',
    price: 2500000000,
    status: PROPERTY_STATUS.COMPLETED,
    type: 'Đất nền',
    area: 120,
    bedrooms: 0,
    bathrooms: 0,
    createdAt: '2025-02-15',
    listingType: LISTING_TYPE.SALE,
    image: 'https://example.com/image1.jpg',
  },
  {
    id: '5',
    title: 'Căn hộ Masteri An Phú',
    address: 'An Phú, Quận 2, TP.HCM',
    price: 4200000000,
    status: PROPERTY_STATUS.PENDING,
    type: 'Căn hộ',
    area: 70,
    bedrooms: 2,
    bathrooms: 2,
    createdAt: '2025-01-22',
    listingType: LISTING_TYPE.SALE,
    image: 'https://example.com/image1.jpg',
  },
];
