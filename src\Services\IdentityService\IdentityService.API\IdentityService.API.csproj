﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="../../../BuildingBlocks/EventBus/EventBus.RabbitMQ/EventBus.RabbitMQ.csproj" />
    <ProjectReference Include="../../../BuildingBlocks/SharedKernel/SharedKernel.csproj" />
    <ProjectReference Include="../IdentityService.Application/IdentityService.Application.csproj" />
    <ProjectReference Include="../IdentityService.Infrastructure/IdentityService.Infrastructure.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>

</Project>