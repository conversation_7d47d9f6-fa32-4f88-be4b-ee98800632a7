import { LISTING_TYPE, PROPERTY_STATUS } from '@/lib/enum';

// types/property.ts
export interface Property {
  id: string;
  title: string;
  address: string;
  price: number;
  status: PROPERTY_STATUS;
  type: '<PERSON><PERSON><PERSON> hộ' | '<PERSON>h<PERSON> phố' | '<PERSON><PERSON><PERSON><PERSON> thự' | 'Đất nền';
  area: number;
  bedrooms: number;
  bathrooms: number;
  createdAt: string;
  image: string;
  listingType: LISTING_TYPE;
}

export type PropertyType = Property['type'];

// Format helpers
