{"ServiceName": "Property", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://*:5002", "Protocols": "Http1"}, "Grpc": {"Url": "http://*:50052", "Protocols": "Http2"}}}, "ConnectionStrings": {"PostgresConnection": "Host=localhost;Database=estate_elite;Username=lau;Password=**********", "RedisConnection": "localhost:6379,password=**********,defaultDatabase=1"}, "RabbitMQ": {"HostName": "localhost", "UserName": "lau", "Password": "**********", "VirtualHost": "/", "Port": 5672, "RetryCount": 10}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/property-service-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "JWT": {"SecretKey": "ZTNtMWJkOTI0eGo5NjN4azQ1aTByYWp4dmVvb3pnbzV2ZTR6cmZwMzR2eGJyNXlibnN4eXJicDdudHh2cnl3bHR1NmVmczVmaG1kNDR2cDYxZ3Vya2JkbGIxMGhwYnhmcnAzOWF4aGg3MHVrMnc0M3dzOWk0NW8zNThqazh4aWg=", "Issuer": "https://localhost:5001", "Audience": "https://localhost:3000", "AccessTokenExpirationInMinutes": 30, "RefreshTokenExpirationInDays": 1, "RefreshTokenSlidingExpirationInMinutes": 30, "TokenType": "Bearer"}, "Minio": {"Endpoint": "http://localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "UseSSL": false, "BucketName": "property-service"}, "DataProtection": {"UseX509Certificate": false, "CertificateThumbprint": ""}, "GrpcEndpoint": {"Identity": "http://localhost:50051", "Property": "http://localhost:50052", "Payment": "http://localhost:50053"}}