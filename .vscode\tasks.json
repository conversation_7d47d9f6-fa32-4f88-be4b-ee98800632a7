{"version": "2.0.0", "tasks": [{"label": "build-all", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/src/Services/IdentityService/IdentityService.API/IdentityService.API.csproj", "${workspaceFolder}/src/Services/PropertyService/PropertyService.API/PropertyService.API.csproj", "${workspaceFolder}/src/Services/PaymentService/PaymentService.API/PaymentService.API.csproj", "${workspaceFolder}/src/Services/FunctionalService/FunctionalService.API.csproj"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "build-identity", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/src/Services/IdentityService/IdentityService.API/IdentityService.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "build-property", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/src/Services/PropertyService/PropertyService.API/PropertyService.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "build-payment", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/src/Services/PaymentService/PaymentService.API/PaymentService.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "build-functional", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/src/Services/FunctionalService/FunctionalService.csproj"], "problemMatcher": "$msCompile"}, {"label": "run-identity", "type": "process", "command": "dotnet", "args": ["run", "--project", "${workspaceFolder}/src/Services/IdentityService/IdentityService.API/IdentityService.API.csproj", "--launch-profile", "http"], "problemMatcher": "$msCompile"}, {"label": "run-property", "type": "process", "command": "dotnet", "args": ["run", "--project", "${workspaceFolder}/src/Services/PropertyService/PropertyService.API/PropertyService.API.csproj", "--launch-profile", "http"], "problemMatcher": "$msCompile"}, {"label": "run-payment", "type": "process", "command": "dotnet", "args": ["run", "--project", "${workspaceFolder}/src/Services/PaymentService/PaymentService.API/PaymentService.API.csproj", "--launch-profile", "http"], "problemMatcher": "$msCompile"}, {"label": "run-functional", "type": "process", "command": "dotnet", "args": ["run", "--project", "${workspaceFolder}/src/Services/FunctionalService/FunctionalService.csproj", "--launch-profile", "http"], "problemMatcher": "$msCompile"}, {"label": "run-all", "dependsOn": ["run-identity", "run-property", "run-payment", "run-functional"], "dependsOrder": "parallel", "problemMatcher": "$msCompile"}]}