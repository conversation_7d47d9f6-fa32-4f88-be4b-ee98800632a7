{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "endOfLine": "lf", "singleAttributePerLine": true, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.ts"}