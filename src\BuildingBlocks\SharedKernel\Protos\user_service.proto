syntax = "proto3";

option csharp_namespace = "SharedKernel.Protos";

package identity.service;

service UserService {
  rpc GetUser (GetUserRequest) returns (GetUserResponse);
}

message GetUserRequest {
  string id = 1;
}

message GetUserResponse {
  string id = 1;
  string full_name = 2;
  string email = 3;
  optional string phone = 4;
  optional string avatar = 6;
  optional string company_name = 7;
  bool accepts_paypal = 8;
  optional string paypal_email = 9;
  optional string paypal_merchant_id = 10;
}
