﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../../BuildingBlocks/SharedKernel/SharedKernel.csproj" />
    <ProjectReference Include="../PropertyService.Application/PropertyService.Application.csproj" />
    <ProjectReference Include="..\PropertyService.Domain\PropertyService.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Extensions/" />
  </ItemGroup>

</Project>
