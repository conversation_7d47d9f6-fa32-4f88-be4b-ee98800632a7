﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="PayPalServerSDK" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../../BuildingBlocks/SharedKernel/SharedKernel.csproj" />
    <ProjectReference Include="../PaymentService.Domain/PaymentService.Domain.csproj" />
    <ProjectReference
      Include="..\..\..\BuildingBlocks\DistributedCache\DistributedCache.Redis\DistributedCache.Redis.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="../../../BuildingBlocks/SharedKernel/Protos/property_service.proto"
      GrpcServices="Client"
      ProtoRoot="../../../BuildingBlocks/SharedKernel/Protos"
    />

    <Protobuf Include="../../../BuildingBlocks/SharedKernel/Protos/user_service.proto"
      GrpcServices="Client"
      ProtoRoot="../../../BuildingBlocks/SharedKernel/Protos"
    />
  </ItemGroup>

</Project>