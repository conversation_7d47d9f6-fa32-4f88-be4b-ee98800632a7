{"version": "0.2.0", "configurations": [{"name": "IdentityService - Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build-identity", "program": "${workspaceFolder}/src/Services/IdentityService/IdentityService.API/bin/Debug/net9.0/IdentityService.API.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/IdentityService/IdentityService.API", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "launchSettingsProfile": "http", "launchBrowser": {"enabled": true}}, {"name": "PropertyService - Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build-property", "program": "${workspaceFolder}/src/Services/PropertyService/PropertyService.API/bin/Debug/net9.0/PropertyService.API.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/PropertyService/PropertyService.API", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "launchSettingsProfile": "http", "launchBrowser": {"enabled": true}}, {"name": "PaymentService - Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build-payment", "program": "${workspaceFolder}/src/Services/PaymentService/PaymentService.API/bin/Debug/net9.0/PaymentService.API.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/PaymentService/PaymentService.API", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "launchSettingsProfile": "http", "launchBrowser": {"enabled": true}}, {"name": "FunctionalService - Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build-functional", "program": "${workspaceFolder}/src/Services/FunctionalService/bin/Debug/net9.0/FunctionalService.dll", "args": [], "cwd": "${workspaceFolder}/src/Services/FunctionalService", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "launchSettingsProfile": "http", "launchBrowser": {"enabled": true}}, {"name": ".NET Core Attach", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}], "compounds": [{"name": "Debug All Services", "configurations": ["IdentityService - Debug", "PropertyService - Debug"]}]}